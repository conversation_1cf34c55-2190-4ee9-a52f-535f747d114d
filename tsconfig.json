{"compilerOptions": {"target": "ES2022", "lib": ["dom", "dom.iterable", "esnext", "webworker"], "types": ["@serwist/next/typings"], "allowJs": true, "skipLibCheck": true, "strict": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "verbatimModuleSyntax": true, "plugins": [{"name": "next"}], "baseUrl": ".", "paths": {"@/*": ["./src/*"]}, "forceConsistentCasingInFileNames": true}, "exclude": ["node_modules", "public/sw.js"], "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"]}