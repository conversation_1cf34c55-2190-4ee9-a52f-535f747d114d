"use client";

import { PerspectiveCamera } from "@react-three/drei";
import { Canvas } from "@react-three/fiber";

export default function CompanionScene() {
  return (
    <div className="h-screen w-screen bg-neutral-500">
      <Canvas
        gl={{ antialias: false }}
        dpr={1}
        camera={{ fov: 75, near: 0.1, far: 1000, position: [0, 0, 5] }}
        style={{ background: "#101010" }}
      >
        {/* Basic Lighting */}
        <ambientLight intensity={0.5} />
        <directionalLight position={[10, 10, 5]} intensity={1} />

        {/* Default Camera */}
        <PerspectiveCamera makeDefault position={[0, 0, 5]} />

        {/* Placeholder for 3D elements to be added later */}
        {/* Example of a mesh with flat shading */}
        <mesh>
          <boxGeometry args={[1, 1, 1]} />
          <meshStandardMaterial color="royalblue" flatShading={true} />
        </mesh>
      </Canvas>
    </div>
  );
}
