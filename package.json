{"name": "ai-companion-app", "version": "0.0.1", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build --turbopack", "start": "next start", "lint": "biome check", "format": "biome format --write"}, "dependencies": {"@radix-ui/react-slot": "^1.2.3", "@tailwindcss/postcss": "^4.1.12", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "install": "^0.13.0", "lucide-react": "^0.542.0", "next": "15.5.0", "postcss": "^8.5.6", "react": "19.1.0", "react-dom": "19.1.0", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.12"}, "devDependencies": {"@biomejs/biome": "2.2.0", "@types/node": "^20.19.11", "@types/react": "^19.1.12", "@types/react-dom": "^19.1.9", "tw-animate-css": "^1.3.7", "typescript": "^5.9.2"}, "packageManager": "pnpm@10.15.0"}